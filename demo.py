#!/usr/bin/env python3
"""
Demo script for AI CLI Terminal Tool
This script demonstrates the key features and capabilities of the tool.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ai_cli.config.manager import ConfigManager
from ai_cli.core.orchestrator import Orchestrator
from ai_cli.ui.cli import CLI
from rich.console import Console
from rich.panel import Panel
from rich.markdown import Markdown


async def demo_basic_functionality():
    """Demonstrate basic functionality without LLM."""
    console = Console()
    
    console.print(Panel(
        Markdown("# 🎯 AI CLI Terminal Tool - Basic Demo\n\nDemonstrating core functionality without LLM integration."),
        title="[bold blue]Demo Mode[/bold blue]",
        border_style="blue"
    ))
    
    try:
        # Initialize components
        config_manager = ConfigManager()
        config = config_manager.load_config()
        orchestrator = Orchestrator(config)
        await orchestrator.initialize()
        
        console.print("\n[green]✅ Core components initialized successfully![/green]")
        
        # Demo internal commands
        console.print("\n[bold cyan]📋 Testing Internal Commands:[/bold cyan]")
        
        commands_to_test = [
            ("/help", "Help system"),
            ("/session start demo-session", "Session management"),
            ("/context list", "Context awareness"),
            ("/config", "Configuration display"),
        ]
        
        for command, description in commands_to_test:
            console.print(f"\n[yellow]Testing: {description}[/yellow]")
            console.print(f"[dim]Command: {command}[/dim]")
            
            response = await orchestrator.process_input(command)
            
            if response.get("type") == "error":
                console.print(f"[red]❌ Error: {response.get('content', 'Unknown error')}[/red]")
            else:
                console.print(f"[green]✅ Success: {response.get('type', 'response')}[/green]")
        
        # Demo file operations (without LLM)
        console.print("\n[bold cyan]📁 Testing File Operations:[/bold cyan]")
        
        # Test file existence check
        test_result = await orchestrator.tooling_engine.execute_tool(
            "file_exists",
            {"path": __file__},
            "demo-session"
        )
        console.print(f"[green]✅ File existence check: {test_result.get('exists', False)}[/green]")
        
        # Test directory listing
        dir_result = await orchestrator.tooling_engine.execute_tool(
            "list_directory",
            {"path": ".", "pattern": "*.py"},
            "demo-session"
        )
        python_files = len(dir_result.get('items', []))
        console.print(f"[green]✅ Directory listing: Found {python_files} Python files[/green]")
        
        # Demo context gathering
        console.print("\n[bold cyan]🔍 Testing Context Gathering:[/bold cyan]")
        context = await orchestrator.context_manager.gather_context("demo query")
        console.print(f"[green]✅ Context gathered: {len(context.get('directory_listing', []))} items[/green]")
        
        # Demo session management
        console.print("\n[bold cyan]💾 Testing Session Management:[/bold cyan]")
        session = orchestrator.session_manager.create_session("demo-session-2")
        console.print(f"[green]✅ Session created: {session.name} ({session.id[:8]}...)[/green]")
        
        # Add some interaction to session
        orchestrator.session_manager.add_interaction(
            session_id=session.id,
            user_input="demo command",
            ai_response="demo response",
            context={"demo": True}
        )
        
        history = orchestrator.session_manager.get_conversation_history(session.id)
        console.print(f"[green]✅ Session history: {len(history)} interactions[/green]")
        
        console.print("\n[bold green]🎉 All basic functionality tests completed successfully![/bold green]")
        
        # Cleanup
        await orchestrator.shutdown()
        
    except Exception as e:
        console.print(f"\n[red]❌ Demo failed: {e}[/red]")
        import traceback
        traceback.print_exc()


async def demo_with_mock_llm():
    """Demonstrate functionality with mock LLM responses."""
    console = Console()
    
    console.print(Panel(
        Markdown("# 🤖 AI CLI Terminal Tool - Mock LLM Demo\n\nDemonstrating AI functionality with simulated responses."),
        title="[bold green]Mock AI Demo[/bold green]",
        border_style="green"
    ))
    
    # This would demonstrate how the tool works with actual LLM integration
    # For now, we'll show the structure and flow
    
    console.print("\n[yellow]Note: This demo shows the structure for LLM integration.[/yellow]")
    console.print("[yellow]To see full AI functionality, configure an LLM provider (Ollama or Deepseek).[/yellow]")
    
    try:
        config_manager = ConfigManager()
        config = config_manager.load_config()
        orchestrator = Orchestrator(config)
        await orchestrator.initialize()
        
        # Show available tools
        tools = orchestrator.tooling_engine.get_available_tools()
        console.print(f"\n[cyan]Available tools for LLM: {', '.join(tools)}[/cyan]")
        
        # Show tool schemas
        schemas = orchestrator.tooling_engine.get_tool_schemas()
        console.print(f"[cyan]Tool schemas generated: {len(schemas)} functions[/cyan]")
        
        # Show what would happen with a complex query
        console.print("\n[bold cyan]🧠 Example Complex Query Processing:[/bold cyan]")
        console.print("[dim]Query: 'Create a Python script that lists all .py files and counts lines in each'[/dim]")
        
        console.print("\n[yellow]LLM would break this down into steps:[/yellow]")
        steps = [
            "1. list_directory(path='.', pattern='*.py')",
            "2. For each file: read_file(path=file_path)",
            "3. Count lines in each file",
            "4. write_file(path='file_analysis.py', content=generated_script)",
            "5. Provide summary to user"
        ]
        
        for step in steps:
            console.print(f"   [green]{step}[/green]")
        
        console.print("\n[bold green]🎯 This demonstrates the autonomous execution capability![/bold green]")
        
        await orchestrator.shutdown()
        
    except Exception as e:
        console.print(f"\n[red]❌ Mock demo failed: {e}[/red]")


def show_setup_instructions():
    """Show setup instructions for full functionality."""
    console = Console()
    
    setup_text = """
# 🚀 Setup Instructions for Full Functionality

## Option 1: Local LLM with Ollama (Recommended for Privacy)

1. **Install Ollama:**
   ```bash
   # Visit https://ollama.ai and follow installation instructions
   # Or use package managers:
   # macOS: brew install ollama
   # Linux: curl -fsSL https://ollama.ai/install.sh | sh
   ```

2. **Download a model:**
   ```bash
   ollama pull llama2
   # or
   ollama pull codellama
   # or
   ollama pull mistral
   ```

3. **Start Ollama server:**
   ```bash
   ollama serve
   ```

4. **Update config:**
   Edit `~/.config/ai_cli_tool/config.yaml`:
   ```yaml
   llm:
     default_provider: "ollama"
     ollama:
       model: "llama2"  # or your preferred model
   ```

## Option 2: Cloud LLM with Deepseek

1. **Get API key:**
   - Visit https://platform.deepseek.com
   - Create account and get API key

2. **Update config:**
   Edit `~/.config/ai_cli_tool/config.yaml`:
   ```yaml
   llm:
     default_provider: "deepseek"
     deepseek:
       api_key: "your-api-key-here"
   ```

## Then run the tool:

```bash
python -m ai_cli.main
# or
ai-cli
```

## Try these commands:

- `list all python files in this directory`
- `search for "python best practices" and summarize`
- `create a simple FastAPI hello world application`
- `refactor main.py to add type hints`
"""
    
    console.print(Panel(
        Markdown(setup_text),
        title="[bold blue]Setup Instructions[/bold blue]",
        border_style="blue"
    ))


async def main():
    """Main demo function."""
    console = Console()
    
    console.print(Panel(
        Markdown("# 🎭 AI CLI Terminal Tool - Interactive Demo\n\nChoose a demo mode to explore the functionality."),
        title="[bold magenta]Welcome to the Demo![/bold magenta]",
        border_style="magenta"
    ))
    
    options = [
        ("1", "Basic Functionality Demo (No LLM required)", demo_basic_functionality),
        ("2", "Mock LLM Demo (Shows AI integration structure)", demo_with_mock_llm),
        ("3", "Setup Instructions (For full AI functionality)", lambda: show_setup_instructions()),
        ("4", "Exit", None),
    ]
    
    for key, description, _ in options:
        console.print(f"[cyan]{key}.[/cyan] {description}")
    
    try:
        choice = input("\nEnter your choice (1-4): ").strip()
        
        for key, description, func in options:
            if choice == key:
                if func is None:
                    console.print("[yellow]Goodbye! 👋[/yellow]")
                    return
                elif asyncio.iscoroutinefunction(func):
                    await func()
                else:
                    func()
                break
        else:
            console.print("[red]Invalid choice. Please run the demo again.[/red]")
            
    except KeyboardInterrupt:
        console.print("\n[yellow]Demo interrupted. Goodbye! 👋[/yellow]")
    except Exception as e:
        console.print(f"\n[red]Demo error: {e}[/red]")


if __name__ == "__main__":
    asyncio.run(main())
