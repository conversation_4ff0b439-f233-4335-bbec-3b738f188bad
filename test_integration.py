#!/usr/bin/env python3
"""
Integration test for AI CLI Terminal Tool
Tests the complete integration of all components working together.
"""

import asyncio
import sys
import tempfile
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ai_cli.config.manager import ConfigManager
from ai_cli.core.orchestrator import Orchestrator
from ai_cli.ui.cli import CLI
from ai_cli.ui.animation import create_ball_animation
from ai_cli.ui.diff_viewer import DiffViewer
from rich.console import Console


async def test_complete_integration():
    """Test complete integration of all components."""
    console = Console()
    console.print("[bold blue]🧪 Running Complete Integration Test[/bold blue]")
    console.print("=" * 60)
    
    success_count = 0
    total_tests = 0
    
    try:
        # Test 1: Configuration System
        total_tests += 1
        console.print("\n1. Testing Configuration System...")
        config_manager = ConfigManager()
        config = config_manager.load_config()
        assert config is not None
        assert hasattr(config, 'llm')
        assert hasattr(config, 'ui')
        assert hasattr(config, 'context')
        console.print("   ✅ Configuration system working")
        success_count += 1
        
        # Test 2: Orchestrator Initialization
        total_tests += 1
        console.print("\n2. Testing Orchestrator Initialization...")
        orchestrator = Orchestrator(config)
        await orchestrator.initialize()
        assert orchestrator.ai_core is not None
        assert orchestrator.tooling_engine is not None
        assert orchestrator.context_manager is not None
        assert orchestrator.session_manager is not None
        console.print("   ✅ Orchestrator initialized successfully")
        success_count += 1
        
        # Test 3: Session Management
        total_tests += 1
        console.print("\n3. Testing Session Management...")
        session = orchestrator.session_manager.create_session("integration-test")
        assert session is not None
        assert session.name == "integration-test"
        
        # Add interaction
        orchestrator.session_manager.add_interaction(
            session_id=session.id,
            user_input="test command",
            ai_response="test response",
            context={"test": True}
        )
        
        history = orchestrator.session_manager.get_conversation_history(session.id)
        assert len(history) == 1
        console.print("   ✅ Session management working")
        success_count += 1
        
        # Test 4: Context Management
        total_tests += 1
        console.print("\n4. Testing Context Management...")
        context = await orchestrator.context_manager.gather_context("test query")
        assert isinstance(context, dict)
        assert 'current_directory' in context
        console.print("   ✅ Context management working")
        success_count += 1
        
        # Test 5: Tool Execution
        total_tests += 1
        console.print("\n5. Testing Tool Execution...")
        
        # Test file operations
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("Hello, World!")
            temp_file = f.name
        
        try:
            # Test file_exists
            result = await orchestrator.tooling_engine.execute_tool(
                "file_exists", {"path": temp_file}, session.id
            )
            assert result.get("exists") is True
            
            # Test read_file
            result = await orchestrator.tooling_engine.execute_tool(
                "read_file", {"path": temp_file}, session.id
            )
            assert "Hello, World!" in result.get("content", "")
            
            console.print("   ✅ Tool execution working")
            success_count += 1
            
        finally:
            os.unlink(temp_file)
        
        # Test 6: Internal Commands
        total_tests += 1
        console.print("\n6. Testing Internal Commands...")
        
        commands_to_test = [
            "/help",
            "/session list",
            "/context list",
            "/config",
        ]
        
        for cmd in commands_to_test:
            response = await orchestrator.process_input(cmd)
            assert response.get("type") != "error", f"Command {cmd} failed: {response.get('content')}"
        
        console.print("   ✅ Internal commands working")
        success_count += 1
        
        # Test 7: Tool Schema Generation
        total_tests += 1
        console.print("\n7. Testing Tool Schema Generation...")
        schemas = orchestrator.tooling_engine.get_tool_schemas()
        assert len(schemas) > 0
        
        # Verify schema structure
        for schema in schemas:
            assert "type" in schema
            assert "function" in schema
            assert "name" in schema["function"]
            assert "description" in schema["function"]
            assert "parameters" in schema["function"]
        
        console.print(f"   ✅ Generated {len(schemas)} tool schemas")
        success_count += 1
        
        # Test 8: UI Components
        total_tests += 1
        console.print("\n8. Testing UI Components...")
        
        # Test diff viewer
        diff_viewer = DiffViewer(console)
        old_text = "Hello\nWorld"
        new_text = "Hello\nUniverse"
        
        # This should not raise an exception
        diff_viewer.show_diff(old_text, new_text, "Old", "New")
        
        console.print("   ✅ UI components working")
        success_count += 1
        
        # Test 9: Animation System
        total_tests += 1
        console.print("\n9. Testing Animation System...")
        
        # Test animation context manager
        async with create_ball_animation(console, "Testing animation"):
            await asyncio.sleep(0.1)  # Brief pause to see animation
        
        console.print("   ✅ Animation system working")
        success_count += 1
        
        # Test 10: Error Handling
        total_tests += 1
        console.print("\n10. Testing Error Handling...")
        
        # Test invalid tool execution
        result = await orchestrator.tooling_engine.execute_tool(
            "invalid_tool", {}, session.id
        )
        assert "error" in result
        
        # Test invalid file operation
        result = await orchestrator.tooling_engine.execute_tool(
            "read_file", {"path": "/nonexistent/file.txt"}, session.id
        )
        assert "error" in result
        
        console.print("   ✅ Error handling working")
        success_count += 1
        
        # Test 11: Configuration Validation
        total_tests += 1
        console.print("\n11. Testing Configuration Validation...")
        
        # Test that configuration has all required sections
        required_sections = ['llm', 'ui', 'context', 'security', 'session']
        for section in required_sections:
            assert hasattr(config, section), f"Missing config section: {section}"
        
        console.print("   ✅ Configuration validation working")
        success_count += 1
        
        # Test 12: Cleanup and Shutdown
        total_tests += 1
        console.print("\n12. Testing Cleanup and Shutdown...")
        
        await orchestrator.shutdown()
        console.print("   ✅ Cleanup and shutdown working")
        success_count += 1
        
        # Final Results
        console.print("\n" + "=" * 60)
        console.print(f"[bold green]🎉 Integration Test Results: {success_count}/{total_tests} tests passed[/bold green]")
        
        if success_count == total_tests:
            console.print("[bold green]✅ ALL TESTS PASSED! The AI CLI Terminal Tool is fully functional.[/bold green]")
            console.print("\n[cyan]Ready for use! Try running:[/cyan]")
            console.print("[yellow]  python -m ai_cli.main[/yellow]")
            console.print("[yellow]  # or[/yellow]")
            console.print("[yellow]  ai-cli[/yellow]")
            return True
        else:
            console.print(f"[bold red]❌ {total_tests - success_count} tests failed[/bold red]")
            return False
            
    except Exception as e:
        console.print(f"\n[bold red]❌ Integration test failed with error: {e}[/bold red]")
        import traceback
        traceback.print_exc()
        return False


async def test_cli_integration():
    """Test CLI integration without actually running the interactive loop."""
    console = Console()
    console.print("\n[bold blue]🖥️  Testing CLI Integration[/bold blue]")
    
    try:
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # Initialize CLI (but don't run the interactive loop)
        cli = CLI(config)
        
        # Test that CLI components are properly initialized
        assert cli.orchestrator is not None
        assert cli.console is not None
        assert cli.diff_viewer is not None
        
        console.print("   ✅ CLI integration working")
        return True
        
    except Exception as e:
        console.print(f"   ❌ CLI integration failed: {e}")
        return False


async def main():
    """Run all integration tests."""
    console = Console()
    
    console.print("[bold magenta]🚀 AI CLI Terminal Tool - Integration Test Suite[/bold magenta]")
    console.print("[dim]This test verifies that all components work together correctly.[/dim]\n")
    
    # Run main integration test
    main_test_passed = await test_complete_integration()
    
    # Run CLI integration test
    cli_test_passed = await test_cli_integration()
    
    # Final summary
    console.print("\n" + "=" * 60)
    if main_test_passed and cli_test_passed:
        console.print("[bold green]🎊 ALL INTEGRATION TESTS PASSED![/bold green]")
        console.print("\n[cyan]The AI CLI Terminal Tool is ready for production use![/cyan]")
        console.print("\n[yellow]Next steps:[/yellow]")
        console.print("1. Configure your LLM provider (Ollama or Deepseek)")
        console.print("2. Run the tool: python -m ai_cli.main")
        console.print("3. Try some commands like '/help' or natural language queries")
        return 0
    else:
        console.print("[bold red]❌ Some integration tests failed[/bold red]")
        console.print("Please check the errors above and fix any issues.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
