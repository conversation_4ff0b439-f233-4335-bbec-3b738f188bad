"""Central orchestration layer that coordinates all components."""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Union

from ai_cli.config.models import Config
from ai_cli.context.manager import ContextManager
from ai_cli.llm.core import AICore
from ai_cli.session.manager import SessionManager
from ai_cli.tools.engine import ToolingEngine
from ai_cli.utils.exceptions import AICliError, CommandExecutionError, LLMError

logger = logging.getLogger(__name__)


class Orchestrator:
    """Central nervous system that coordinates all components."""

    def __init__(self, config: Config) -> None:
        """Initialize orchestrator with configuration.

        Args:
            config: Application configuration
        """
        self.config = config

        # Initialize core components
        self.context_manager = ContextManager(config)
        self.session_manager = SessionManager(config)
        self.tooling_engine = ToolingEngine(config)
        self.ai_core = AICore(config, self.tooling_engine)

        logger.info("Orchestrator initialized")

    async def initialize(self) -> None:
        """Initialize async components."""
        await self.ai_core.initialize()

    async def process_input(self, user_input: str, session_id: Optional[str] = None) -> Dict[str, Any]:
        """Process user input and return response.

        Args:
            user_input: Raw user input string
            session_id: Optional session identifier

        Returns:
            Dictionary containing response data
        """
        try:
            # Get or create session
            session = self.session_manager.get_or_create_session(session_id)

            # Check for internal commands first
            if user_input.startswith('/'):
                return await self._handle_internal_command(user_input, session)

            # Gather context
            context = await self.context_manager.gather_context(user_input)

            # Get conversation history
            history = self.session_manager.get_conversation_history(session.id)

            # Process with AI
            response = await self.ai_core.process_query(
                query=user_input,
                context=context,
                history=history,
                session_id=session.id
            )

            # Update session
            self.session_manager.add_interaction(
                session_id=session.id,
                user_input=user_input,
                ai_response=response.get('content', ''),
                context=context
            )

            return response

        except Exception as e:
            logger.error(f"Error processing input: {e}")
            return {
                'type': 'error',
                'content': f"Error processing your request: {str(e)}",
                'error': str(e)
            }

    async def _handle_internal_command(self, command: str, session: Any) -> Dict[str, Any]:
        """Handle internal commands that start with /.

        Args:
            command: Internal command string
            session: Current session object

        Returns:
            Dictionary containing response data
        """
        parts = command[1:].split()
        cmd = parts[0].lower() if parts else ""
        args = parts[1:] if len(parts) > 1 else []

        try:
            if cmd == "help":
                return await self._handle_help_command()
            elif cmd == "session":
                return await self._handle_session_command(args)
            elif cmd == "context":
                return await self._handle_context_command(args)
            elif cmd == "config":
                return await self._handle_config_command(args)
            elif cmd == "history":
                return await self._handle_history_command(args, session.id)
            elif cmd == "clear":
                return await self._handle_clear_command()
            elif cmd == "exit" or cmd == "quit":
                return {"type": "exit", "content": "Goodbye! 👋"}
            else:
                return {
                    "type": "error",
                    "content": f"Unknown command: /{cmd}. Type /help for available commands."
                }
        except Exception as e:
            logger.error(f"Error handling internal command: {e}")
            return {
                "type": "error",
                "content": f"Error executing command: {str(e)}"
            }

    async def _handle_help_command(self) -> Dict[str, Any]:
        """Handle help command."""
        help_text = """
**Available Commands:**

**Internal Commands:**
- `/help` - Show this help message
- `/session start [name]` - Start a new session
- `/session list` - List all sessions
- `/session switch <id>` - Switch to a session
- `/session end` - End current session
- `/context add <path>` - Add file/directory to context
- `/context remove <path>` - Remove from context
- `/context list` - Show current context
- `/context clear` - Clear all context
- `/history` - Show conversation history
- `/clear` - Clear screen
- `/exit` or `/quit` - Exit the application

**Natural Language Commands:**
You can ask me to help with various tasks:
- File operations: "list python files", "read main.py", "create a new file"
- Shell commands: "check git status", "run tests", "install dependencies"
- Web searches: "search for python best practices"
- Code analysis: "explain this function", "refactor this code"
- Complex tasks: "set up a new Python project with tests"

**Examples:**
- `list all .py files in the src directory`
- `search for "async python patterns" and summarize`
- `refactor main.py to add type hints`
- `run the test suite and show me any failures`
"""
        return {"type": "help", "content": help_text}

    async def _handle_session_command(self, args: List[str]) -> Dict[str, Any]:
        """Handle session management commands."""
        if not args:
            return {"type": "error", "content": "Session command requires an action (start, list, switch, end)"}

        action = args[0].lower()

        if action == "start":
            name = args[1] if len(args) > 1 else None
            session = self.session_manager.create_session(name)
            return {"type": "session", "content": f"Started new session: {session.name} ({session.id})"}

        elif action == "list":
            sessions = self.session_manager.list_sessions()
            if not sessions:
                return {"type": "session", "content": "No sessions found"}

            session_list = "\n".join([
                f"- {s.name} ({s.id}) - {s.created_at.strftime('%Y-%m-%d %H:%M')}"
                for s in sessions
            ])
            return {"type": "session", "content": f"Available sessions:\n{session_list}"}

        elif action == "switch":
            if len(args) < 2:
                return {"type": "error", "content": "Switch command requires a session ID"}

            session_id = args[1]
            session = self.session_manager.get_session(session_id)
            if not session:
                return {"type": "error", "content": f"Session not found: {session_id}"}

            self.session_manager.set_current_session(session_id)
            return {"type": "session", "content": f"Switched to session: {session.name} ({session.id})"}

        elif action == "end":
            current = self.session_manager.get_current_session()
            if current:
                self.session_manager.end_session(current.id)
                return {"type": "session", "content": f"Ended session: {current.name}"}
            else:
                return {"type": "session", "content": "No active session to end"}

        else:
            return {"type": "error", "content": f"Unknown session action: {action}"}

    async def _handle_context_command(self, args: List[str]) -> Dict[str, Any]:
        """Handle context management commands."""
        if not args:
            return {"type": "error", "content": "Context command requires an action (add, remove, list, clear)"}

        action = args[0].lower()

        if action == "add":
            if len(args) < 2:
                return {"type": "error", "content": "Add command requires a path"}

            path = args[1]
            await self.context_manager.add_explicit_context(path)
            return {"type": "context", "content": f"Added to context: {path}"}

        elif action == "remove":
            if len(args) < 2:
                return {"type": "error", "content": "Remove command requires a path"}

            path = args[1]
            self.context_manager.remove_explicit_context(path)
            return {"type": "context", "content": f"Removed from context: {path}"}

        elif action == "list":
            context = await self.context_manager.gather_context("")
            if not context.get('files'):
                return {"type": "context", "content": "No context items"}

            context_list = "\n".join([
                f"- {item['path']} ({item['type']})"
                for item in context.get('files', [])
            ])
            return {"type": "context", "content": f"Current context:\n{context_list}"}

        elif action == "clear":
            self.context_manager.clear_explicit_context()
            return {"type": "context", "content": "Context cleared"}

        else:
            return {"type": "error", "content": f"Unknown context action: {action}"}

    async def _handle_config_command(self, args: List[str]) -> Dict[str, Any]:
        """Handle configuration commands."""
        # For now, just show current configuration
        config_info = f"""
**Current Configuration:**
- LLM Provider: {self.config.llm.default_provider}
- Animation: {'Enabled' if self.config.ui.enable_animation else 'Disabled'}
- Command Confirmation: {'Enabled' if self.config.ui.confirm_shell_commands else 'Disabled'}
- Auto Context: {'Enabled' if self.config.context.auto_context else 'Disabled'}
"""
        return {"type": "config", "content": config_info}

    async def _handle_history_command(self, args: List[str], session_id: str) -> Dict[str, Any]:
        """Handle history command."""
        history = self.session_manager.get_conversation_history(session_id)

        if not history:
            return {"type": "history", "content": "No conversation history"}

        # Show last 10 interactions by default
        limit = 10
        if args and args[0].isdigit():
            limit = int(args[0])

        recent_history = history[-limit:]
        history_text = "\n".join([
            f"**User:** {item['user_input']}\n**AI:** {item['ai_response'][:200]}{'...' if len(item['ai_response']) > 200 else ''}\n"
            for item in recent_history
        ])

        return {"type": "history", "content": f"Recent conversation history:\n\n{history_text}"}

    async def _handle_clear_command(self) -> Dict[str, Any]:
        """Handle clear screen command."""
        return {"type": "clear", "content": ""}

    async def shutdown(self) -> None:
        """Gracefully shutdown the orchestrator."""
        logger.info("Shutting down orchestrator")

        # Save any pending session data
        await self.session_manager.save_all_sessions()

        # Cleanup resources
        await self.ai_core.cleanup()
        await self.tooling_engine.cleanup()
