#!/usr/bin/env python3
"""Basic functionality test for AI CLI Terminal Tool."""

import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ai_cli.config.manager import ConfigManager
from ai_cli.config.models import Config
from ai_cli.core.orchestrator import Orchestrator
from ai_cli.tools.engine import ToolingEngine
from ai_cli.llm.core import AICore
from ai_cli.context.manager import ContextManager
from ai_cli.session.manager import SessionManager


async def test_basic_components():
    """Test basic component initialization."""
    print("🧪 Testing AI CLI Terminal Tool Components")
    print("=" * 50)

    try:
        # Test configuration loading
        print("1. Testing Configuration Manager...")
        config_manager = ConfigManager()
        config = config_manager.load_config()
        print(f"   ✅ Configuration loaded successfully")
        print(f"   - Default LLM Provider: {config.llm.default_provider}")
        print(f"   - Animation Enabled: {config.ui.enable_animation}")

        # Test context manager
        print("\n2. Testing Context Manager...")
        context_manager = ContextManager(config)
        context = await context_manager.gather_context("test query")
        print(f"   ✅ Context gathered successfully")
        print(f"   - Current Directory: {context.get('current_directory', 'N/A')}")
        print(f"   - Directory Items: {len(context.get('directory_listing', []))}")

        # Test session manager
        print("\n3. Testing Session Manager...")
        session_manager = SessionManager(config)
        session = session_manager.create_session("test-session")
        print(f"   ✅ Session created successfully")
        print(f"   - Session ID: {session.id}")
        print(f"   - Session Name: {session.name}")

        # Test tooling engine
        print("\n4. Testing Tooling Engine...")
        tooling_engine = ToolingEngine(config)
        schemas = tooling_engine.get_tool_schemas()
        available_tools = tooling_engine.get_available_tools()
        print(f"   ✅ Tooling engine initialized successfully")
        print(f"   - Available Tools: {len(available_tools)}")
        print(f"   - Tool Schemas: {len(schemas)}")
        print(f"   - Tools: {', '.join(available_tools[:5])}...")

        # Test AI Core (without actual LLM calls)
        print("\n5. Testing AI Core...")
        ai_core = AICore(config, tooling_engine)
        provider_info = ai_core.get_current_provider_info()
        print(f"   ✅ AI Core initialized successfully")
        if provider_info:
            print(f"   - Current Provider: {provider_info.get('provider', 'N/A')}")
            print(f"   - Model: {provider_info.get('model', 'N/A')}")
        else:
            print(f"   - No provider available (this is expected without API keys)")

        # Test orchestrator
        print("\n6. Testing Orchestrator...")
        orchestrator = Orchestrator(config)
        await orchestrator.initialize()
        print(f"   ✅ Orchestrator initialized successfully")

        # Test a simple internal command
        print("\n7. Testing Internal Commands...")
        help_response = await orchestrator.process_input("/help")
        print(f"   ✅ Help command processed successfully")
        print(f"   - Response Type: {help_response.get('type', 'N/A')}")

        # Test file operations tool
        print("\n8. Testing File Operations...")
        file_result = await tooling_engine.execute_tool(
            "file_exists",
            {"path": __file__},
            "test-session"
        )
        print(f"   ✅ File operations test completed")
        print(f"   - Test file exists: {file_result.get('exists', False)}")

        # Test directory listing
        print("\n9. Testing Directory Listing...")
        dir_result = await tooling_engine.execute_tool(
            "list_directory",
            {"path": ".", "pattern": "*.py"},
            "test-session"
        )
        print(f"   ✅ Directory listing test completed")
        print(f"   - Python files found: {len(dir_result.get('items', []))}")

        print("\n" + "=" * 50)
        print("🎉 All basic functionality tests passed!")
        print("\nNext steps:")
        print("1. Configure your LLM provider (Ollama or Deepseek)")
        print("2. Run 'python -m ai_cli.main' to start the tool")
        print("3. Try some commands like '/help' or 'list python files'")

        # Cleanup
        await orchestrator.shutdown()

    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

    return True


async def test_tool_schemas():
    """Test tool schema generation."""
    print("\n🔧 Testing Tool Schemas")
    print("-" * 30)

    try:
        config = ConfigManager().load_config()
        tooling_engine = ToolingEngine(config)
        schemas = tooling_engine.get_tool_schemas()

        print(f"Generated {len(schemas)} tool schemas:")
        for i, schema in enumerate(schemas[:3], 1):  # Show first 3
            func_info = schema.get("function", {})
            name = func_info.get("name", "Unknown")
            desc = func_info.get("description", "No description")
            print(f"  {i}. {name}: {desc[:60]}...")

        if len(schemas) > 3:
            print(f"  ... and {len(schemas) - 3} more tools")

        return True

    except Exception as e:
        print(f"❌ Tool schema test failed: {e}")
        return False


if __name__ == "__main__":
    async def main():
        success = await test_basic_components()
        if success:
            await test_tool_schemas()

    asyncio.run(main())
