#!/bin/bash
# WSL Installation Script for AI CLI Terminal Tool

set -e

echo "🐧 AI CLI Terminal Tool - WSL Installation"
echo "=========================================="

# Get the current directory (where the project is)
PROJECT_DIR="$(pwd)"
echo "Project directory: $PROJECT_DIR"

# Check if we're in the right directory
if [ ! -f "ai_cli_wrapper.py" ]; then
    echo "❌ Error: ai_cli_wrapper.py not found. Please run this script from the project directory."
    exit 1
fi

# Check Python version
python_version=$(python3 --version 2>&1 | cut -d' ' -f2 | cut -d'.' -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Error: Python $required_version or higher is required. Found: $python_version"
    exit 1
fi

echo "✅ Python $python_version detected"

# Install Poetry dependencies (for development)
if command -v poetry &> /dev/null; then
    echo "📦 Installing dependencies with Poetry..."

    # Update lock file if needed
    if ! poetry check --lock &> /dev/null; then
        echo "🔄 Updating Poetry lock file..."
        poetry lock
    fi

    poetry install
    echo "✅ Poetry dependencies installed"
else
    echo "⚠️  Poetry not found, skipping Poetry installation"
    echo "📦 Installing system dependencies..."
    pip3 install --user python-dotenv pyyaml rich prompt-toolkit requests httpx beautifulsoup4 trafilatura pydantic
    echo "✅ System dependencies installed"
fi

# Create the global wrapper script
echo "🔧 Creating global ai-cli command..."

# Check if Poetry is available and use it, otherwise use system Python
if command -v poetry &> /dev/null; then
    echo "Using Poetry environment..."
    sudo tee /usr/local/bin/ai-cli > /dev/null << EOF
#!/bin/bash
cd "$PROJECT_DIR"
poetry run python ai_cli_wrapper.py "\$@"
EOF
else
    echo "Using system Python..."
    sudo tee /usr/local/bin/ai-cli > /dev/null << EOF
#!/bin/bash
cd "$PROJECT_DIR"
python3 ai_cli_wrapper.py "\$@"
EOF
fi

# Make it executable
sudo chmod +x /usr/local/bin/ai-cli

echo "✅ Global ai-cli command created"

# Create configuration directory and file
echo "⚙️  Setting up configuration..."

CONFIG_DIR="$HOME/.config/ai_cli_tool"
CONFIG_FILE="$CONFIG_DIR/config.yaml"

mkdir -p "$CONFIG_DIR"

if [ ! -f "$CONFIG_FILE" ]; then
    if [ -f "config/config.example.yaml" ]; then
        cp "config/config.example.yaml" "$CONFIG_FILE"
        echo "✅ Configuration file created at $CONFIG_FILE"
    else
        # Create a basic config file
        cat > "$CONFIG_FILE" << 'EOF'
# AI CLI Terminal Tool Configuration
llm:
  default_provider: "ollama"
  deepseek:
    api_key: ""
    model: "deepseek-chat"
    base_url: "https://api.deepseek.com/v1"
  ollama:
    base_url: "http://localhost:11434"
    model: "llama2"

ui:
  enable_animation: true
  confirm_shell_commands: true
  syntax_highlighting: true
  show_timestamps: true

context:
  auto_context: true
  max_file_size: 1048576
  exclude_patterns:
    - "*.pyc"
    - "__pycache__"
    - ".git"
    - "node_modules"
    - "*.log"

security:
  require_command_confirmation: true
  blocked_commands:
    - "rm -rf /"
    - "sudo rm"
    - "format"

session:
  auto_save: true
  max_sessions: 50
  max_history_entries: 1000

logging:
  level: "INFO"
  file_logging: true
  log_file: "logs/ai_cli.log"
EOF
        echo "✅ Basic configuration file created at $CONFIG_FILE"
    fi
else
    echo "✅ Configuration file already exists at $CONFIG_FILE"
fi

# Test the installation
echo "🧪 Testing installation..."

if ai-cli version &> /dev/null; then
    echo "✅ Installation successful!"
else
    echo "❌ Installation test failed"
    exit 1
fi

# Check for Ollama
echo "🔍 Checking for Ollama..."
if curl -s http://localhost:11434/api/tags &> /dev/null; then
    echo "✅ Ollama server detected"
else
    echo "ℹ️  Ollama not detected. You can:"
    echo "   - Install Ollama: curl -fsSL https://ollama.ai/install.sh | sh"
    echo "   - Or use Deepseek API (add API key to config)"
fi

echo ""
echo "🎉 Installation completed!"
echo ""
echo "Next steps:"
echo "1. Configure your LLM provider:"
echo "   nano $CONFIG_FILE"
echo ""
echo "2. Start the tool:"
echo "   ai-cli"
echo ""
echo "3. Try some commands:"
echo "   ai-cli --help"
echo "   ai-cli version"
echo "   ai-cli init-config"
echo ""
echo "4. In the tool, try:"
echo "   /help"
echo "   list files in current directory"
echo "   /exit"
echo ""

# Show current status
echo "📊 Current status:"
echo "   Command: $(which ai-cli)"
echo "   Config:  $CONFIG_FILE"
echo "   Project: $PROJECT_DIR"
