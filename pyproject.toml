[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "ai-cli-terminal"
version = "0.1.0"
description = "AI-Powered CLI Terminal Tool with LLM integration and autonomous task execution"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "ai_cli"}]

[tool.poetry.dependencies]
python = "^3.8"
rich = "^13.7.0"
prompt-toolkit = "^3.0.43"
typer = "^0.12.0"
requests = "^2.31.0"
httpx = "^0.25.2"
pyyaml = "^6.0.1"
python-dotenv = "^1.0.0"
beautifulsoup4 = "^4.12.2"
trafilatura = "^1.6.4"
gitpython = "^3.1.40"
psutil = "^5.9.6"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
black = "^23.11.0"
ruff = "^0.1.6"
mypy = "^1.7.1"

[tool.poetry.scripts]
ai-cli = "ai_cli.main:app"

[tool.black]
line-length = 88
target-version = ['py38']

[tool.ruff]
line-length = 88
target-version = "py38"
select = ["E", "F", "W", "C90", "I", "N", "UP", "YTT", "S", "BLE", "FBT", "B", "A", "COM", "C4", "DTZ", "T10", "EM", "EXE", "FA", "ISC", "ICN", "G", "INP", "PIE", "T20", "PYI", "PT", "Q", "RSE", "RET", "SLF", "SIM", "TID", "TCH", "INT", "ARG", "PTH", "ERA", "PD", "PGH", "PL", "TRY", "FLY", "NPY", "AIR", "PERF", "FURB", "LOG", "RUF"]
ignore = ["S101", "S603", "S607"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
