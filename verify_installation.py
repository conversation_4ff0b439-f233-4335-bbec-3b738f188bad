#!/usr/bin/env python3
"""
Verification script for AI CLI Terminal Tool installation.
This script checks that all components are properly installed and configured.
"""

import asyncio
import sys
import importlib
import subprocess
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.markdown import Markdown


def check_python_version():
    """Check Python version compatibility."""
    if sys.version_info < (3, 8):
        return False, f"Python {sys.version_info.major}.{sys.version_info.minor} (requires 3.8+)"
    return True, f"Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"


def check_dependencies():
    """Check if all required dependencies are installed."""
    required_packages = [
        'rich',
        'prompt_toolkit',
        'pydantic',
        'pyyaml',
        'requests',
        'httpx',
        'beautifulsoup4',
        'duckduckgo_search',
        'python_dotenv',
        'typer',
    ]
    
    results = []
    for package in required_packages:
        try:
            importlib.import_module(package.replace('-', '_'))
            results.append((package, True, "✅ Installed"))
        except ImportError:
            results.append((package, False, "❌ Missing"))
    
    return results


def check_ai_cli_modules():
    """Check if AI CLI modules can be imported."""
    modules = [
        'ai_cli.config.manager',
        'ai_cli.core.orchestrator',
        'ai_cli.llm.core',
        'ai_cli.tools.engine',
        'ai_cli.ui.cli',
        'ai_cli.session.manager',
        'ai_cli.context.manager',
    ]
    
    results = []
    for module in modules:
        try:
            importlib.import_module(module)
            results.append((module, True, "✅ OK"))
        except ImportError as e:
            results.append((module, False, f"❌ Error: {e}"))
    
    return results


def check_configuration():
    """Check configuration file."""
    config_dir = Path.home() / ".config" / "ai_cli_tool"
    config_file = config_dir / "config.yaml"
    
    if not config_file.exists():
        return False, f"❌ Config file not found at {config_file}"
    
    try:
        with open(config_file, 'r') as f:
            content = f.read()
            if 'llm:' in content and 'ui:' in content:
                return True, f"✅ Config file exists at {config_file}"
            else:
                return False, f"❌ Config file incomplete at {config_file}"
    except Exception as e:
        return False, f"❌ Error reading config: {e}"


def check_ollama():
    """Check if Ollama is available."""
    try:
        result = subprocess.run(
            ["curl", "-s", "http://localhost:11434/api/tags"],
            capture_output=True,
            timeout=5
        )
        if result.returncode == 0:
            return True, "✅ Ollama server running"
        else:
            return False, "❌ Ollama server not responding"
    except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
        return False, "❌ Ollama not available"


def check_console_script():
    """Check if the ai-cli console script is available."""
    try:
        result = subprocess.run(
            ["ai-cli", "--help"],
            capture_output=True,
            timeout=10
        )
        if result.returncode == 0:
            return True, "✅ Console script working"
        else:
            return False, f"❌ Console script error (exit code: {result.returncode})"
    except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
        return False, "❌ Console script not found"


async def check_basic_functionality():
    """Check basic functionality without LLM."""
    try:
        # Add project root to path
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        from ai_cli.config.manager import ConfigManager
        from ai_cli.core.orchestrator import Orchestrator
        
        # Test configuration loading
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # Test orchestrator initialization
        orchestrator = Orchestrator(config)
        await orchestrator.initialize()
        
        # Test internal command
        response = await orchestrator.process_input("/help")
        
        # Cleanup
        await orchestrator.shutdown()
        
        if response.get("type") == "help":
            return True, "✅ Basic functionality working"
        else:
            return False, f"❌ Unexpected response: {response.get('type')}"
            
    except Exception as e:
        return False, f"❌ Error: {e}"


async def main():
    """Main verification function."""
    console = Console()
    
    console.print(Panel(
        Markdown("# 🔍 AI CLI Terminal Tool - Installation Verification\n\nChecking installation status and configuration..."),
        title="[bold blue]Verification Report[/bold blue]",
        border_style="blue"
    ))
    
    # Create results table
    table = Table(title="Verification Results")
    table.add_column("Component", style="cyan")
    table.add_column("Status", style="green")
    table.add_column("Details", style="dim")
    
    all_passed = True
    
    # Check Python version
    passed, details = check_python_version()
    table.add_row("Python Version", "✅ OK" if passed else "❌ FAIL", details)
    if not passed:
        all_passed = False
    
    # Check dependencies
    console.print("\n[yellow]Checking dependencies...[/yellow]")
    dep_results = check_dependencies()
    failed_deps = [pkg for pkg, passed, _ in dep_results if not passed]
    
    if failed_deps:
        table.add_row("Dependencies", "❌ FAIL", f"Missing: {', '.join(failed_deps)}")
        all_passed = False
    else:
        table.add_row("Dependencies", "✅ OK", f"All {len(dep_results)} packages installed")
    
    # Check AI CLI modules
    console.print("[yellow]Checking AI CLI modules...[/yellow]")
    module_results = check_ai_cli_modules()
    failed_modules = [mod for mod, passed, _ in module_results if not passed]
    
    if failed_modules:
        table.add_row("AI CLI Modules", "❌ FAIL", f"Failed: {len(failed_modules)} modules")
        all_passed = False
    else:
        table.add_row("AI CLI Modules", "✅ OK", f"All {len(module_results)} modules imported")
    
    # Check configuration
    console.print("[yellow]Checking configuration...[/yellow]")
    passed, details = check_configuration()
    table.add_row("Configuration", "✅ OK" if passed else "❌ FAIL", details)
    if not passed:
        all_passed = False
    
    # Check console script
    console.print("[yellow]Checking console script...[/yellow]")
    passed, details = check_console_script()
    table.add_row("Console Script", "✅ OK" if passed else "⚠️ WARN", details)
    
    # Check Ollama (optional)
    console.print("[yellow]Checking Ollama...[/yellow]")
    passed, details = check_ollama()
    table.add_row("Ollama (Optional)", "✅ OK" if passed else "ℹ️ INFO", details)
    
    # Check basic functionality
    console.print("[yellow]Checking basic functionality...[/yellow]")
    passed, details = await check_basic_functionality()
    table.add_row("Basic Functionality", "✅ OK" if passed else "❌ FAIL", details)
    if not passed:
        all_passed = False
    
    # Display results
    console.print(table)
    
    # Summary
    if all_passed:
        console.print(Panel(
            Markdown("""
# 🎉 Installation Verified Successfully!

The AI CLI Terminal Tool is properly installed and ready to use.

## Next Steps:
1. **Configure LLM Provider**: Edit `~/.config/ai_cli_tool/config.yaml`
   - For Ollama: Ensure Ollama is running with a model
   - For Deepseek: Add your API key
2. **Start the tool**: Run `ai-cli` or `python -m ai_cli.main`
3. **Try commands**: Type `/help` for available commands

## Example Commands:
- `list all python files in this directory`
- `search for "python best practices"`
- `create a simple hello world script`
"""),
            title="[bold green]✅ Ready to Use![/bold green]",
            border_style="green"
        ))
        return 0
    else:
        console.print(Panel(
            Markdown("""
# ❌ Installation Issues Detected

Some components are not working correctly. Please:

1. **Install missing dependencies**: `pip install -e .` or `poetry install`
2. **Check configuration**: Ensure config file exists and is valid
3. **Verify Python version**: Requires Python 3.8+
4. **Run installation script**: `python install.py`

For help, check the README.md file or run the installation script again.
"""),
            title="[bold red]❌ Issues Found[/bold red]",
            border_style="red"
        ))
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
