#!/usr/bin/env python3
"""Simple wrapper for AI CLI Terminal Tool to avoid Typer compatibility issues."""

import sys
import asyncio
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from ai_cli.config.manager import ConfigManager
from ai_cli.core.orchestrator import Orchestrator
from ai_cli.ui.cli import CLI
from ai_cli.utils.logging import setup_logging
from rich.console import Console

console = Console()


def show_help():
    """Show help information."""
    help_text = """
AI CLI Terminal Tool v0.1.0

Usage: ai-cli [COMMAND] [OPTIONS]

Commands:
  main         Start the AI CLI Terminal Tool (default)
  init-config  Initialize configuration file
  version      Show version information
  help         Show this help message

Options:
  --verbose, -v    Enable verbose logging
  --debug, -d      Enable debug mode
  --config, -c     Path to configuration file

Examples:
  ai-cli                    # Start the tool
  ai-cli main --debug       # Start with debug mode
  ai-cli init-config        # Create configuration file
  ai-cli version            # Show version
"""
    console.print(help_text)


def show_version():
    """Show version information."""
    console.print("AI CLI Terminal Tool v0.1.0")


def init_config():
    """Initialize configuration."""
    try:
        config_manager = ConfigManager()
        config_path = config_manager.init_config()
        console.print(f"[green]Configuration initialized at: {config_path}[/green]")
        console.print("[yellow]Please edit the configuration file to add your API keys.[/yellow]")
    except Exception as e:
        console.print(f"[red]Error initializing configuration: {e}[/red]")
        sys.exit(1)


async def start_main(verbose=False, debug=False, config_path=None):
    """Start the main CLI application."""
    try:
        # Setup logging
        setup_logging(verbose=verbose, debug=debug)
        
        # Load configuration
        config_manager = ConfigManager(config_path)
        config = config_manager.load_config()
        
        # Initialize core components
        orchestrator = Orchestrator(config)
        cli = CLI(orchestrator, config)
        
        # Start the CLI interface
        await cli.run()
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Goodbye! 👋[/yellow]")
        sys.exit(0)
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        if debug:
            console.print_exception()
        sys.exit(1)


def main():
    """Main entry point."""
    args = sys.argv[1:]
    
    # Parse basic arguments
    verbose = "--verbose" in args or "-v" in args
    debug = "--debug" in args or "-d" in args
    config_path = None
    
    # Remove flags from args
    args = [arg for arg in args if arg not in ["--verbose", "-v", "--debug", "-d"]]
    
    # Handle config path
    if "--config" in args:
        idx = args.index("--config")
        if idx + 1 < len(args):
            config_path = Path(args[idx + 1])
            args = args[:idx] + args[idx + 2:]
    elif "-c" in args:
        idx = args.index("-c")
        if idx + 1 < len(args):
            config_path = Path(args[idx + 1])
            args = args[:idx] + args[idx + 2:]
    
    # Handle commands
    if not args or args[0] == "main":
        asyncio.run(start_main(verbose=verbose, debug=debug, config_path=config_path))
    elif args[0] == "version":
        show_version()
    elif args[0] == "init-config":
        init_config()
    elif args[0] == "help" or args[0] == "--help" or args[0] == "-h":
        show_help()
    else:
        console.print(f"[red]Unknown command: {args[0]}[/red]")
        console.print("Use 'ai-cli help' for available commands.")
        sys.exit(1)


if __name__ == "__main__":
    main()
