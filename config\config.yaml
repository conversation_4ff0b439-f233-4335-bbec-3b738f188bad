# AI CLI Terminal Tool Configuration

# LLM Configuration
llm:
  # Default provider: "ollama" or "deepseek"
  default_provider: "deepseek"
  
  # Deepseek API configuration
  deepseek:
    api_key: "***********************************"
    model: "deepseek-chat, deepseek-reasoner"
    base_url: "https://api.deepseek.com/v1"
  
  # Ollama configuration (local LLM)
  ollama:
    base_url: "http://localhost:11434"
    model: "llama2"  # Change to your preferred Ollama model

# UI Configuration
ui:
  # Enable/disable the ball animation during processing
  enable_animation: true
  
  # Require confirmation before executing shell commands
  confirm_shell_commands: true
  
  # Animation speed (seconds between frames)
  animation_speed: 0.1
  
  # Maximum lines to display in output
  max_output_lines: 1000000
  
  # Enable syntax highlighting
  syntax_highlighting: true
  
  # Show timestamps in responses
  show_timestamps: true

# Context Management
context:
  # Automatically gather context from environment
  auto_context: true
  
  # Maximum file size to include in context (bytes)
  max_file_size: 1048576  # 1MB
  
  # Patterns to exclude from context
  exclude_patterns:
    - "*.pyc"
    - "*.pyo"
    - "__pycache__"
    - ".git"
    - "node_modules"
    - "*.log"
    - "*.tmp"
    - ".env"
    - "*.key"
    - "*.pem"
  
  # Include Git repository information
  include_git_info: true
  
  # Maximum number of files to include in context
  max_context_files: 100

# Security Configuration
security:
  # Require confirmation before executing commands
  require_command_confirmation: true
  
  # Commands that are always allowed (empty list means all need confirmation)
  allowed_commands: []
  
  # Commands that are blocked
  blocked_commands:
    - "rm -rf /"
    - "sudo rm"
    - "format"
    - "del /s"
    - "rmdir /s"
  
  # Maximum command length
  max_command_length: 1000

# Session Management
session:
  # Automatically save session data
  auto_save: true
  
  # Maximum number of conversation entries to keep
  max_history_entries: 1000
  
  # Session timeout in seconds (1 hour)
  session_timeout: 3600
  
  # Persist context between sessions
  persist_context: true

# Web Search Configuration
web_search:
  # Enable web search functionality
  enabled: true
  
  # Search provider: "duckduckgo", "google", "bing"
  provider: "duckduckgo"
  
  # API key for search provider (if required)
  api_key: ""
  
  # Maximum number of search results
  max_results: 5
  
  # Request timeout in seconds
  timeout: 100
