"""Main entry point for the AI CLI Terminal Tool."""

import asyncio
import sys
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console

from ai_cli.config.manager import ConfigManager
from ai_cli.core.orchestrator import Orchestrator
from ai_cli.ui.cli import CLI
from ai_cli.utils.logging import setup_logging

app = typer.Typer(
    name="ai-cli",
    help="AI-Powered CLI Terminal Tool with LLM integration",
    no_args_is_help=True,
    rich_markup_mode="rich",
)

console = Console()


@app.command()
def main(
    config_path: Optional[Path] = typer.Option(
        None,
        "--config",
        "-c",
        help="Path to configuration file",
    ),
    verbose: bool = typer.Option(
        False,
        "--verbose",
        "-v",
        help="Enable verbose logging",
    ),
    debug: bool = typer.Option(
        False,
        "--debug",
        "-d",
        help="Enable debug mode",
    ),
) -> None:
    """Start the AI CLI Terminal Tool."""
    try:
        # Setup logging
        setup_logging(verbose=verbose, debug=debug)

        # Load configuration
        config_manager = ConfigManager(config_path)
        config = config_manager.load_config()

        # Initialize core components
        orchestrator = Orchestrator(config)
        cli = CLI(orchestrator, config)

        # Start the CLI interface
        asyncio.run(cli.run())

    except KeyboardInterrupt:
        console.print("\n[yellow]Goodbye! 👋[/yellow]")
        sys.exit(0)
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        if debug:
            console.print_exception()
        sys.exit(1)


@app.command()
def init_config(
    path: Optional[Path] = typer.Option(
        None,
        "--path",
        "-p",
        help="Path to create configuration file",
    ),
    force: bool = typer.Option(
        False,
        "--force",
        "-f",
        help="Overwrite existing configuration",
    ),
) -> None:
    """Initialize configuration file with default settings."""
    try:
        config_manager = ConfigManager()
        config_path = config_manager.init_config(path, force)
        console.print(f"[green]Configuration initialized at: {config_path}[/green]")
        console.print("[yellow]Please edit the configuration file to add your API keys.[/yellow]")
    except Exception as e:
        console.print(f"[red]Error initializing configuration: {e}[/red]")
        sys.exit(1)


@app.command()
def version() -> None:
    """Show version information."""
    from ai_cli import __version__
    console.print(f"AI CLI Terminal Tool v{__version__}")


if __name__ == "__main__":
    app()
